import React, { useState } from 'react';
import { Input, Modal, Calendar, Button, Space, Radio, Tooltip } from 'antd';
import { CalendarOutlined } from '@ant-design/icons';
import type { Moment } from 'moment';
import moment from 'moment';

type SelectionMode = 'single' | 'range';

interface MultiDatePickerProps {
  value?: string[];
  onChange?: (dates: string[]) => void;
  placeholder?: string;
  format?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
  defaultSelectionMode?: SelectionMode;
}

const MultiDatePicker: React.FC<MultiDatePickerProps> = ({
  value = [],
  onChange,
  placeholder = '请选择日期',
  format = 'YYYY-MM-DD',
  disabled = false,
  style,
  className,
  defaultSelectionMode = 'single',
}) => {
  const [visible, setVisible] = useState(false);
  const [selectedDates, setSelectedDates] = useState<string[]>(value);
  const [selectionMode, setSelectionMode] = useState<SelectionMode>(defaultSelectionMode);
  const [rangeStart, setRangeStart] = useState<string | null>(null);
  const [rangeEnd, setRangeEnd] = useState<string | null>(null);

  // 处理单个日期选择
  const handleSingleDateSelect = (date: Moment) => {
    const dateStr = date.format(format);
    const newSelectedDates = [...selectedDates];

    const index = newSelectedDates.indexOf(dateStr);
    if (index > -1) {
      // 如果日期已存在，则移除
      newSelectedDates.splice(index, 1);
    } else {
      // 如果日期不存在，则添加
      newSelectedDates.push(dateStr);
    }

    // 按日期排序
    newSelectedDates.sort();
    setSelectedDates(newSelectedDates);
  };

  // 处理范围日期选择
  const handleRangeDateSelect = (date: Moment) => {
    const dateStr = date.format(format);

    if (!rangeStart) {
      // 设置范围开始
      setRangeStart(dateStr);
      setRangeEnd(null);
    } else if (!rangeEnd) {
      // 设置范围结束并生成范围内的所有日期
      const start = moment(rangeStart);
      const end = moment(dateStr);

      // 确保开始日期小于结束日期
      const actualStart = start.isBefore(end) ? start : end;
      const actualEnd = start.isBefore(end) ? end : start;

      const rangeDates: string[] = [];
      const current = actualStart.clone();

      while (current.isSameOrBefore(actualEnd)) {
        rangeDates.push(current.format(format));
        current.add(1, 'day');
      }

      // 合并已选择的日期和新的范围日期
      const newSelectedDates = [...selectedDates];
      rangeDates.forEach(rangeDate => {
        if (!newSelectedDates.includes(rangeDate)) {
          newSelectedDates.push(rangeDate);
        }
      });

      // 按日期排序
      newSelectedDates.sort();
      setSelectedDates(newSelectedDates);

      // 重置范围选择
      setRangeStart(null);
      setRangeEnd(null);
    } else {
      // 重新开始范围选择
      setRangeStart(dateStr);
      setRangeEnd(null);
    }
  };

  // 处理日期选择
  const handleDateSelect = (date: Moment) => {
    if (selectionMode === 'single') {
      handleSingleDateSelect(date);
    } else {
      handleRangeDateSelect(date);
    }
  };

  // 确定选择
  const handleOk = () => {
    onChange?.(selectedDates);
    setVisible(false);
  };

  // 取消选择
  const handleCancel = () => {
    setSelectedDates(value);
    setVisible(false);
  };

  // 清空选择
  const handleClear = () => {
    setSelectedDates([]);
    setRangeStart(null);
    setRangeEnd(null);
  };

  // 切换选择模式
  const handleModeChange = (mode: SelectionMode) => {
    setSelectionMode(mode);
    setRangeStart(null);
    setRangeEnd(null);
  };

  // 显示的文本
  const displayText = value.length > 0 ? value.join(', ') : '';

  // 自定义日期单元格渲染
  const dateCellRender = (date: Moment) => {
    const dateStr = date.format(format);
    const isSelected = selectedDates.includes(dateStr);
    const isRangeStart = rangeStart === dateStr;
    const isRangeEnd = rangeEnd === dateStr;
    const isInRange = rangeStart && rangeEnd &&
      moment(dateStr).isBetween(moment(rangeStart), moment(rangeEnd), 'day', '[]');

    let backgroundColor = 'transparent';
    let color = 'inherit';
    let border = 'none';

    if (isSelected) {
      backgroundColor = '#1890ff';
      color = '#fff';
    } else if (isRangeStart) {
      backgroundColor = '#52c41a';
      color = '#fff';
      border = '2px solid #389e0d';
    } else if (isInRange) {
      backgroundColor = '#f6ffed';
      color = '#52c41a';
      border = '1px solid #b7eb8f';
    }

    return (
      <div
        style={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor,
          color,
          border,
          borderRadius: '2px',
          cursor: 'pointer',
          position: 'relative',
        }}
        onClick={() => handleDateSelect(date)}
      >
        {date.date()}
        {isRangeStart && (
          <div style={{
            position: 'absolute',
            top: '-2px',
            right: '-2px',
            width: '6px',
            height: '6px',
            backgroundColor: '#52c41a',
            borderRadius: '50%',
          }} />
        )}
      </div>
    );
  };

  return (
    <>
      <Input
        value={displayText}
        placeholder={placeholder}
        readOnly
        disabled={disabled}
        style={style}
        className={className}
        suffix={<CalendarOutlined />}
        onClick={() => !disabled && setVisible(true)}
        onFocus={() => !disabled && setVisible(true)}
      />
      
      <Modal
        title="选择日期"
        open={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={500}
        footer={[
          <Button key="clear" onClick={handleClear}>
            清空
          </Button>,
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Button key="ok" type="primary" onClick={handleOk}>
            确定
          </Button>,
        ]}
      >
        <div style={{ padding: '16px 0' }}>
          {/* 选择模式切换 */}
          <div style={{ marginBottom: 16 }}>
            <div style={{ marginBottom: 8, fontWeight: 'bold' }}>选择模式：</div>
            <Radio.Group
              value={selectionMode}
              onChange={(e) => handleModeChange(e.target.value)}
              style={{ width: '100%' }}
            >
              <Radio.Button value="single" style={{ width: '50%', textAlign: 'center' }}>
                单独选择
              </Radio.Button>
              <Radio.Button value="range" style={{ width: '50%', textAlign: 'center' }}>
                连续选择
              </Radio.Button>
            </Radio.Group>
          </div>

          {/* 选择提示 */}
          <div style={{
            marginBottom: 16,
            padding: 8,
            backgroundColor: '#f0f9ff',
            border: '1px solid #91d5ff',
            borderRadius: 4,
            fontSize: '12px'
          }}>
            {selectionMode === 'single' ? (
              <span>💡 单独选择模式：点击日期进行选择/取消选择</span>
            ) : (
              <span>
                💡 连续选择模式：
                {!rangeStart ? '点击第一个日期设置起始点' :
                 '点击第二个日期完成范围选择'}
                {rangeStart && (
                  <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                    （起始：{rangeStart}）
                  </span>
                )}
              </span>
            )}
          </div>

          <Calendar
            fullscreen={false}
            dateFullCellRender={dateCellRender}
          />

          {selectedDates.length > 0 && (
            <div style={{ marginTop: 16 }}>
              <div style={{ marginBottom: 8, fontWeight: 'bold' }}>
                已选择的日期 ({selectedDates.length}):
              </div>
              <div style={{
                maxHeight: 120,
                overflowY: 'auto',
                padding: 8,
                backgroundColor: '#f5f5f5',
                borderRadius: 4,
                border: '1px solid #d9d9d9'
              }}>
                {selectedDates.map((date, index) => (
                  <span key={date} style={{
                    marginRight: 8,
                    marginBottom: 4,
                    display: 'inline-block',
                    padding: '2px 6px',
                    backgroundColor: '#e6f7ff',
                    border: '1px solid #91d5ff',
                    borderRadius: 3,
                    fontSize: '12px'
                  }}>
                    {date}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </Modal>
    </>
  );
};

export default MultiDatePicker;
