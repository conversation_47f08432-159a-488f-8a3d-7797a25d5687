import React, { useState } from 'react';
import { ConfigProvider, Space, Typography, Card } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import 'antd/dist/antd.css';
import MultiDatePicker from './MultiDatePicker';
import './App.css';

const { Title, Text } = Typography;

function App() {
  const [selectedDates, setSelectedDates] = useState<string[]>([]);

  const handleDateChange = (dates: string[]) => {
    setSelectedDates(dates);
    console.log('选择的日期:', dates);
  };

  return (
    <ConfigProvider locale={zhCN}>
      <div className="App" style={{ padding: '40px', backgroundColor: '#f0f2f5', minHeight: '100vh' }}>
        <div style={{ maxWidth: 800, margin: '0 auto' }}>
          <Title level={2} style={{ textAlign: 'center', marginBottom: 40 }}>
            多日期选择组件演示
          </Title>

          <Card title="多日期选择器 - 支持单选和连选" style={{ marginBottom: 24 }}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <div>
                <Text strong>多功能日期选择器：</Text>
                <MultiDatePicker
                  value={selectedDates}
                  onChange={handleDateChange}
                  placeholder="点击选择多个日期（支持单选和连选）"
                  style={{ width: '100%', marginTop: 8 }}
                  defaultSelectionMode="single"
                />
              </div>

              {selectedDates.length > 0 && (
                <div>
                  <Text strong>当前选择的日期 ({selectedDates.length} 个)：</Text>
                  <div style={{
                    marginTop: 8,
                    padding: 12,
                    backgroundColor: '#f6ffed',
                    border: '1px solid #b7eb8f',
                    borderRadius: 6,
                    maxHeight: 120,
                    overflowY: 'auto'
                  }}>
                    {selectedDates.map((date, index) => (
                      <span key={date} style={{
                        marginRight: 8,
                        marginBottom: 4,
                        display: 'inline-block',
                        padding: '4px 8px',
                        backgroundColor: '#e6f7ff',
                        border: '1px solid #91d5ff',
                        borderRadius: 4,
                        fontSize: '12px'
                      }}>
                        {date}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </Space>
          </Card>

          <Card title="使用说明">
            <Space direction="vertical">
              <Text strong style={{ color: '#1890ff' }}>基本操作：</Text>
              <Text>• 点击日期框打开日期选择弹窗</Text>
              <Text>• 点击"确定"按钮保存选择并关闭弹窗</Text>
              <Text>• 选择的日期会按时间顺序排列显示</Text>

              <Text strong style={{ color: '#52c41a', marginTop: 16 }}>单独选择模式：</Text>
              <Text>• 点击日期进行选择/取消选择</Text>
              <Text>• 支持选择多个不连续的日期</Text>
              <Text>• 已选择的日期显示为蓝色背景</Text>

              <Text strong style={{ color: '#fa8c16', marginTop: 16 }}>连续选择模式：</Text>
              <Text>• 第一次点击设置起始日期（绿色背景）</Text>
              <Text>• 第二次点击设置结束日期，自动选择范围内所有日期</Text>
              <Text>• 可以多次进行连续选择，新选择的日期会添加到已选列表</Text>
              <Text>• 支持与单独选择模式混合使用</Text>

              <Text strong style={{ color: '#722ed1', marginTop: 16 }}>高级功能：</Text>
              <Text>• 可以随时切换选择模式</Text>
              <Text>• 支持清空所有已选择的日期</Text>
              <Text>• 实时显示选择状态和提示信息</Text>
            </Space>
          </Card>
        </div>
      </div>
    </ConfigProvider>
  );
}

export default App;
