[{"D:\\demo\\日期\\multi-date-picker\\src\\index.tsx": "1", "D:\\demo\\日期\\multi-date-picker\\src\\reportWebVitals.ts": "2", "D:\\demo\\日期\\multi-date-picker\\src\\App.tsx": "3", "D:\\demo\\日期\\multi-date-picker\\src\\MultiDatePicker.tsx": "4"}, {"size": 554, "mtime": 1752222230060, "results": "5", "hashOfConfig": "6"}, {"size": 425, "mtime": 1752222230050, "results": "7", "hashOfConfig": "6"}, {"size": 4195, "mtime": 1754880651304, "results": "8", "hashOfConfig": "6"}, {"size": 9040, "mtime": 1754880610853, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "19fzied", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\demo\\日期\\multi-date-picker\\src\\index.tsx", [], [], "D:\\demo\\日期\\multi-date-picker\\src\\reportWebVitals.ts", [], [], "D:\\demo\\日期\\multi-date-picker\\src\\App.tsx", [], [], "D:\\demo\\日期\\multi-date-picker\\src\\MultiDatePicker.tsx", ["22", "23", "24"], [], {"ruleId": "25", "severity": 1, "message": "26", "line": 2, "column": 42, "nodeType": "27", "messageId": "28", "endLine": 2, "endColumn": 47}, {"ruleId": "25", "severity": 1, "message": "29", "line": 2, "column": 56, "nodeType": "27", "messageId": "28", "endLine": 2, "endColumn": 63}, {"ruleId": "25", "severity": 1, "message": "30", "line": 145, "column": 11, "nodeType": "27", "messageId": "28", "endLine": 145, "endColumn": 21}, "@typescript-eslint/no-unused-vars", "'Space' is defined but never used.", "Identifier", "unusedVar", "'Tooltip' is defined but never used.", "'isRangeEnd' is assigned a value but never used."]